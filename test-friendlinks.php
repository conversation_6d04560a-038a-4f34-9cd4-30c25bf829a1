<?php
/**
 * 友链功能测试文件
 * 用于测试修改后的友链样式和功能
 */

// 加载WordPress环境
require_once('wp-config.php');
require_once('wp-load.php');

// 检查链接管理器是否启用
$link_manager_enabled = get_option('link_manager_enabled');
echo "<h2>链接管理器状态: " . ($link_manager_enabled ? '已启用' : '未启用') . "</h2>";

// 获取所有友链
$friendlinks = get_bookmarks(array(
    'orderby' => 'name',
    'order' => 'ASC'
));

echo "<h2>当前友链数量: " . count($friendlinks) . "</h2>";

if (count($friendlinks) > 0) {
    echo "<h3>友链列表:</h3>";
    foreach ($friendlinks as $link) {
        echo "<p>ID: {$link->link_id}, 名称: {$link->link_name}, URL: {$link->link_url}</p>";
    }
    
    echo "<h3>友链样式测试:</h3>";
    echo "<h4>样式1 (默认):</h4>";
    echo do_shortcode('[friendlinks style="1"]');
    
    echo "<h4>样式1-方形:</h4>";
    echo do_shortcode('[friendlinks style="1-square"]');
    
} else {
    echo "<p>没有找到友链数据。请先在WordPress后台添加一些友链。</p>";
    echo "<p>访问: <a href='wp-admin/link-add.php'>添加友链</a></p>";
}

// 添加CSS样式
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
p { margin: 10px 0; }
</style>';
?>
